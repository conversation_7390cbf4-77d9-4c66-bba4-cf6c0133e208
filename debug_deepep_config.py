#!/usr/bin/env python3
"""
DeepEP配置调试脚本
用于分析和验证EP配置参数，预测断言是否会失败
"""

def analyze_deepep_config():
    """分析DeepEP配置参数"""
    
    # 常量定义（来自internode.cu）
    NUM_MAX_NVL_PEERS = 8  # 从configs.cuh
    kNumCombineForwarderWarps = 24  # 从internode.cu第1827行
    
    # 配置映射（来自buffer.py第246-257行）
    config_map = {
        2: (10, 256, 6, 128),    # (nvl_send, nvl_recv, rdma_send, rdma_recv)
        4: (9, 256, 6, 128),
        8: (4, 256, 6, 128),
        16: (4, 288, 12, 128),
        24: (1, 288, 8, 128),
        32: (1, 288, 8, 128),
        64: (1, 288, 20, 128),
        128: (1, 560, 12, 128),
        144: (2, 720, 8, 128),
        160: (2, 720, 8, 128),
    }
    
    print("=" * 80)
    print("DeepEP配置分析报告")
    print("=" * 80)
    
    for num_ranks in sorted(config_map.keys()):
        nvl_send, nvl_recv, rdma_send, rdma_recv = config_map[num_ranks]
        
        # 计算关键参数（模拟internode.cu的计算）
        num_rdma_ranks = num_ranks // NUM_MAX_NVL_PEERS
        num_warps_per_forwarder = max(kNumCombineForwarderWarps // num_rdma_ranks, 1)
        
        # 断言检查
        assertion_pass = rdma_send >= num_warps_per_forwarder
        
        print(f"\n【EP Ranks: {num_ranks}】")
        print(f"  配置参数: Config(num_sms, {nvl_send}, {nvl_recv}, {rdma_send}, {rdma_recv})")
        print(f"  计算过程:")
        print(f"    - num_rdma_ranks = {num_ranks} / {NUM_MAX_NVL_PEERS} = {num_rdma_ranks}")
        print(f"    - num_warps_per_forwarder = max({kNumCombineForwarderWarps} / {num_rdma_ranks}, 1) = {num_warps_per_forwarder}")
        print(f"  断言检查:")
        print(f"    - num_max_rdma_chunked_send_tokens ({rdma_send}) >= num_warps_per_forwarder ({num_warps_per_forwarder})")
        print(f"    - 结果: {'✅ PASS' if assertion_pass else '❌ FAIL'}")
        
        if not assertion_pass:
            recommended_rdma_send = num_warps_per_forwarder
            print(f"  🔧 修复建议:")
            print(f"    - 将 rdma_send 从 {rdma_send} 改为 {recommended_rdma_send}")
            print(f"    - 新配置: Config(num_sms, {nvl_send}, {nvl_recv}, {recommended_rdma_send}, {rdma_recv})")

def predict_vllm_config():
    """预测vLLM配置的EP大小"""
    print("\n" + "=" * 80)
    print("vLLM配置预测")
    print("=" * 80)
    
    # 用户的vLLM配置
    tensor_parallel_size = 8
    data_parallel_size = 2
    enable_expert_parallel = True
    
    print(f"vLLM启动参数:")
    print(f"  - tensor-parallel-size: {tensor_parallel_size}")
    print(f"  - data-parallel-size: {data_parallel_size}")
    print(f"  - enable-expert-parallel: {enable_expert_parallel}")
    
    if enable_expert_parallel:
        # 根据vLLM源码分析，EP大小的计算
        ep_size = data_parallel_size * tensor_parallel_size
        print(f"\nEP大小计算:")
        print(f"  - EP group大小 = data_parallel_size × tensor_parallel_size")
        print(f"  - EP group大小 = {data_parallel_size} × {tensor_parallel_size} = {ep_size}")
        
        return ep_size
    else:
        print(f"\nExpert Parallel未启用")
        return None

if __name__ == "__main__":
    # 分析所有配置
    analyze_deepep_config()
    
    # 预测用户的配置
    predicted_ep_size = predict_vllm_config()
    
    if predicted_ep_size:
        print(f"\n🎯 根据您的vLLM配置，预测EP大小为: {predicted_ep_size}")
        print(f"请重点关注上面 'EP Ranks: {predicted_ep_size}' 的分析结果")
